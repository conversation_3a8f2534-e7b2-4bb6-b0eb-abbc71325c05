import requests
import j<PERSON>

def test_simple_device_recommender():
    """Test the simplified device recommender that only provides TurboMedics links"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🧪 === TESTING SIMPLIFIED DEVICE RECOMMENDER ===\n")
    
    # Test 1: Direct device recommendation query
    print("1️⃣ Testing direct device recommendation query...")
    device_query = {
        "user_id": "test_simple_device",
        "session_id": "simple_device_session",
        "query": "Can you recommend some health devices?",
        "model": "qwen2.5:1.5b"
    }
    
    test_query(base_url, device_query, "Direct device recommendation")
    
    # Test 2: Device shopping query
    print("\n2️⃣ Testing device shopping query...")
    shopping_query = {
        "user_id": "test_simple_device",
        "session_id": "simple_device_session",
        "query": "What health devices should I buy?",
        "model": "qwen2.5:1.5b"
    }
    
    test_query(base_url, shopping_query, "Device shopping")
    
    # Test 3: Health monitoring query
    print("\n3️⃣ Testing health monitoring query...")
    monitoring_query = {
        "user_id": "test_simple_device",
        "session_id": "simple_device_session",
        "query": "I want to monitor my blood pressure at home",
        "model": "qwen2.5:1.5b"
    }
    
    test_query(base_url, monitoring_query, "Health monitoring")
    
    # Test 4: General health query (should show proactive TurboMedics link)
    print("\n4️⃣ Testing general health query...")
    general_query = {
        "user_id": "test_simple_device",
        "session_id": "simple_device_session",
        "query": "How can I improve my health?",
        "model": "qwen2.5:1.5b"
    }
    
    test_query(base_url, general_query, "General health (proactive)")
    
    print("\n🎉 === SIMPLIFIED DEVICE RECOMMENDER TESTING COMPLETED ===")

def test_query(base_url, query_data, test_name):
    """Helper function to test a chat query"""
    try:
        response = requests.post(f"{base_url}/query", json=query_data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ {test_name} - SUCCESS")
            
            # Check response content
            response_text = result.get("response", "")
            tools_used = result.get("tools_used", [])
            
            print(f"   📝 Response length: {len(response_text)} characters")
            print(f"   🔧 Tools used: {', '.join(tools_used) if tools_used else 'None'}")
            
            # Check for TurboMedics link
            if "turbomedics.com" in response_text.lower():
                print(f"   🔗 ✅ TurboMedics link included")
            else:
                print(f"   🔗 ❌ TurboMedics link missing")
            
            # Check that it's simplified (no complex device analysis)
            complex_indicators = [
                "missing measurements", "abnormal vitals", "multi-function devices",
                "specialized devices", "recommendation summary", "device analysis"
            ]
            
            complex_found = [indicator for indicator in complex_indicators 
                           if indicator in response_text.lower()]
            
            if complex_found:
                print(f"   ⚠️ Complex content found: {', '.join(complex_found)}")
            else:
                print(f"   ✅ Simple response (no complex analysis)")
            
            # Show a snippet of the response
            snippet = response_text[:150] + "..." if len(response_text) > 150 else response_text
            print(f"   💬 Response snippet: {snippet}")
            
        else:
            print(f"   ❌ {test_name} - FAILED: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ {test_name} - ERROR: {e}")

if __name__ == "__main__":
    test_simple_device_recommender()
