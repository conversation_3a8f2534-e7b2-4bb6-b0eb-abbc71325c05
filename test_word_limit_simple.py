import requests
import json

def test_word_limit():
    """Simple test for word limit validation"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🧪 === TESTING WORD LIMIT VALIDATION ===\n")
    
    # Test 1: Long query (should be rejected)
    print("1️⃣ Testing long query (>300 words)...")
    
    long_query = " ".join(["word"] * 350)  # 350 words
    
    long_request = {
        "user_id": "test_user",
        "session_id": "test_session",
        "query": long_query,
        "model": "qwen2.5:1.5b"
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=long_request, timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "error" in result:
                print(f"   ✅ PASS: Long query rejected")
                print(f"   📊 Word count: {result.get('word_count', 'Unknown')}")
                print(f"   📝 Error: {result['error'][:80]}...")
            else:
                print(f"   ❌ FAIL: Long query was accepted")
        else:
            print(f"   ❌ Request failed with status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Normal query (should be accepted)
    print("\n2️⃣ Testing normal query (<300 words)...")
    
    normal_request = {
        "user_id": "test_user",
        "session_id": "test_session", 
        "query": "Hello, how are you?",
        "model": "qwen2.5:1.5b"
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=normal_request, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "error" not in result:
                print(f"   ✅ PASS: Normal query accepted")
                print(f"   📝 Response length: {len(result.get('response', ''))} chars")
            else:
                print(f"   ❌ FAIL: Normal query rejected: {result['error']}")
        else:
            print(f"   ❌ Request failed with status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Boundary test (exactly 300 words)
    print("\n3️⃣ Testing boundary (exactly 300 words)...")
    
    boundary_query = " ".join(["word"] * 300)  # Exactly 300 words
    
    boundary_request = {
        "user_id": "test_user",
        "session_id": "test_session",
        "query": boundary_query,
        "model": "qwen2.5:1.5b"
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=boundary_request, timeout=30)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if "error" not in result:
                print(f"   ✅ PASS: 300-word query accepted")
            else:
                print(f"   ❌ FAIL: 300-word query rejected: {result['error']}")
        else:
            print(f"   ❌ Request failed with status: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n🎉 === WORD LIMIT TESTING COMPLETED ===")

if __name__ == "__main__":
    test_word_limit()
