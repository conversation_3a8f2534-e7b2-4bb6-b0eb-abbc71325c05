import requests
import json

# Test the mental health assessment with the same structure as Streamlit
def test_streamlit_mental_health():
    url = "http://127.0.0.1:8002/mental-health-assessment"
    
    # Simulate the exact data structure that Streamlit would send
    # This mimics what st.session_state.mental_health_assessment_data would contain
    assessment_data = {
        "age": 25,
        "gender": "Male", 
        "country": "United States",
        "recent_stress_event": False,
        "stress_responses": {
            "work": [3, 2, 3, 4, 2, 3, 2, 3, 4, 2]
        },
        "phq9_responses": [1, 1, 2, 1, 1, 0, 1, 0, 0],
        "gad7_responses": [1, 2, 1, 1, 2, 1, 1]
    }
    
    # Create the payload exactly as the Streamlit app would
    payload = {
        "user_id": "test_user_streamlit",
        "assessment_data": assessment_data
    }
    
    print("=== Testing Streamlit-style Mental Health Assessment ===")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        # Send the request with explicit JSON content type
        headers = {"Content-Type": "application/json"}
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"\nResponse Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {json.dumps(result, indent=2)}")
        elif response.status_code == 422:
            print("❌ 422 Validation Error")
            try:
                error_detail = response.json()
                print(f"Error Details: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Raw Response: {response.text}")
        else:
            print(f"❌ Unexpected Status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Request Exception: {e}")

if __name__ == "__main__":
    test_streamlit_mental_health()
