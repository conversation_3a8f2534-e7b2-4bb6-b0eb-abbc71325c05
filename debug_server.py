#!/usr/bin/env python3
"""
Debug version of the server to identify issues
"""

import sys
import traceback

try:
    print("Starting server debug...")
    
    # Test imports first
    print("Testing imports...")
    
    from fastapi import FastAPI, Request, Form, File, UploadFile, HTTPException
    print("✅ FastAPI imports OK")
    
    from fastapi.responses import JSONResponse
    from fastapi.exceptions import RequestValidationError
    print("✅ FastAPI additional imports OK")
    
    import uvicorn
    import json
    import logging
    from datetime import datetime
    print("✅ Standard library imports OK")
    
    # Test tool imports
    print("Testing tool imports...")
    sys.path.append('tools')
    
    from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
    print("✅ Mental health assessment tool import OK")
    
    # Test tool initialization
    print("Testing tool initialization...")
    mental_health_tool = MentalHealthAssessmentTool()
    print("✅ Mental health assessment tool initialization OK")
    
    # Test a simple assessment
    print("Testing simple assessment...")
    test_data = {
        "age": 25,
        "gender": "Male",
        "country": "United States",
        "recent_stress_event": False,
        "stress_responses": {"work": [3, 2, 3, 4, 2, 3, 2, 3, 4, 2]},
        "phq9_responses": [1, 1, 2, 1, 1, 0, 1, 0, 0],
        "gad7_responses": [1, 2, 1, 1, 2, 1, 1]
    }
    
    result = mental_health_tool.comprehensive_assessment(test_data)
    print("✅ Mental health assessment execution OK")
    print(f"Assessment result keys: {list(result.keys())}")
    
    # Now try to start the server
    print("Starting FastAPI server...")
    
    app = FastAPI(title="Debug Health Agent API")
    
    from pydantic import BaseModel
    from typing import Dict, Any
    
    class MentalHealthAssessmentRequest(BaseModel):
        user_id: str
        assessment_data: Dict[str, Any]
    
    @app.post("/mental-health-assessment")
    async def mental_health_assessment_endpoint(request: MentalHealthAssessmentRequest):
        try:
            user_id = request.user_id
            assessment_data = request.assessment_data
            
            print(f"Processing assessment for user: {user_id}")
            print(f"Assessment data keys: {list(assessment_data.keys())}")
            
            # Initialize tool
            tool = MentalHealthAssessmentTool()
            
            # Perform assessment
            result = tool.comprehensive_assessment(assessment_data)
            
            print(f"Assessment completed successfully")
            return result
            
        except Exception as e:
            error_msg = f"Error in mental health assessment: {str(e)}"
            print(f"ERROR: {error_msg}")
            print(f"Traceback: {traceback.format_exc()}")
            return {"error": error_msg}
    
    @app.get("/")
    async def root():
        return {"message": "Debug Health Agent API is running"}
    
    print("✅ FastAPI app created successfully")
    print("Starting server on port 8003...")
    
    uvicorn.run(app, host="127.0.0.1", port=8003)
    
except Exception as e:
    print(f"❌ ERROR: {e}")
    print(f"Traceback: {traceback.format_exc()}")
    sys.exit(1)
