import requests
import json

def test_mental_health_swagger():
    """Test the mental health assessment endpoint with proper data structure"""
    
    url = "http://127.0.0.1:8002/mental-health-assessment"
    
    # Create test data that matches the expected schema
    test_payload = {
        "user_id": "test_user_swagger",
        "assessment_data": {
            "age": 28,
            "gender": "Female",
            "country": "United States",
            "recent_stress_event": True,
            "stress_responses": {
                "work": [4, 3, 4, 5, 3, 4, 3, 4, 5, 3],
                "relationship": [2, 3, 2, 3, 2, 2, 3, 2, 3, 2]
            },
            "phq9_responses": [2, 2, 3, 2, 1, 1, 2, 1, 0],
            "gad7_responses": [2, 3, 2, 2, 3, 2, 2]
        }
    }
    
    print("=== Testing Mental Health Assessment Endpoint ===")
    print(f"URL: {url}")
    print(f"Payload Structure:")
    print(json.dumps(test_payload, indent=2))
    
    try:
        # Send POST request
        response = requests.post(url, json=test_payload)
        
        print(f"\n=== Response ===")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS! Mental Health Assessment Response:")
            
            # Pretty print the key parts of the response
            if "assessments" in result:
                assessments = result["assessments"]
                print(f"\n📊 Assessment Results:")
                
                if "ml_risk_prediction" in assessments:
                    risk = assessments["ml_risk_prediction"]
                    print(f"  Risk Level: {risk.get('risk_level', 'Unknown')}")
                    print(f"  Confidence: {risk.get('confidence', 0):.1f}%")
                
                if "depression_phq9" in assessments:
                    dep = assessments["depression_phq9"]
                    print(f"  Depression Score: {dep.get('total_score', 0)}/27 - {dep.get('severity', 'Unknown')}")
                
                if "anxiety_gad7" in assessments:
                    anx = assessments["anxiety_gad7"]
                    print(f"  Anxiety Score: {anx.get('total_score', 0)}/21 - {anx.get('severity', 'Unknown')}")
                
                if "stress_burnout" in assessments:
                    stress = assessments["stress_burnout"]
                    print(f"  Stress Categories:")
                    for category, scores in stress.items():
                        if isinstance(scores, dict):
                            print(f"    {category.title()}: {scores.get('total_score', 0)}/{scores.get('max_score', 50)} - {scores.get('interpretation', 'Unknown')}")
            
            if "summary" in result:
                print(f"\n📝 Summary: {result['summary']}")
            
            if "recommendations" in result and result["recommendations"]:
                print(f"\n💡 Recommendations:")
                for i, rec in enumerate(result["recommendations"][:3], 1):  # Show first 3
                    print(f"  {i}. {rec}")
            
            if "crisis_resources" in result and result["crisis_resources"]:
                crisis = result["crisis_resources"]
                if "resources" in crisis:
                    print(f"\n🆘 Crisis Resources Available: {len(crisis['resources'])} resources")
            
            print(f"\n✅ Full response structure is valid!")
            
        elif response.status_code == 422:
            print("❌ 422 Validation Error:")
            try:
                error_detail = response.json()
                print(json.dumps(error_detail, indent=2))
            except:
                print(f"Raw error response: {response.text}")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Server might not be running on port 8002")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_mental_health_swagger()
