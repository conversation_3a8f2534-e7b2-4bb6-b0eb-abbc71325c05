import requests
import json

def test_all_endpoints():
    """Test all endpoints to ensure complete functionality"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🔬 === COMPLETE ENDPOINT TESTING ===\n")
    
    # Test 1: Root endpoint
    print("1️⃣ Testing Root Endpoint (GET /)...")
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Root endpoint working: {result['status']}")
        else:
            print(f"   ❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Root endpoint error: {e}")
    
    # Test 2: Health endpoint
    print("\n2️⃣ Testing Health Endpoint (GET /health)...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Health endpoint working: {result['status']}")
            print(f"   🔧 Mental health tool: {result['mental_health_tool']}")
        else:
            print(f"   ❌ Health endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Health endpoint error: {e}")
    
    # Test 3: Supported countries
    print("\n3️⃣ Testing Supported Countries (GET /supported-countries)...")
    try:
        response = requests.get(f"{base_url}/supported-countries")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Countries endpoint working: {result['total_countries']} countries")
            print(f"   🌍 Sample countries: {', '.join(result['supported_countries'][:5])}")
        else:
            print(f"   ❌ Countries endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Countries endpoint error: {e}")
    
    # Test 4: Mental health assessment (Low Risk)
    print("\n4️⃣ Testing Mental Health Assessment - Low Risk...")
    low_risk_payload = {
        "user_id": "endpoint_test_low",
        "assessment_data": {
            "age": 22,
            "gender": "Male",
            "country": "United States",
            "recent_stress_event": False,
            "stress_responses": {"work": [1, 2, 1, 2, 1, 2, 1, 2, 1, 2]},
            "phq9_responses": [0, 0, 0, 1, 0, 0, 0, 0, 0],
            "gad7_responses": [0, 0, 1, 0, 0, 1, 0]
        }
    }
    
    test_mental_health_assessment(base_url, low_risk_payload, "Low Risk")
    
    # Test 5: Mental health assessment (High Risk)
    print("\n5️⃣ Testing Mental Health Assessment - High Risk...")
    high_risk_payload = {
        "user_id": "endpoint_test_high",
        "assessment_data": {
            "age": 45,
            "gender": "Female",
            "country": "Canada",
            "recent_stress_event": True,
            "stress_responses": {
                "work": [4, 5, 4, 5, 4, 5, 4, 5, 4, 5],
                "relationship": [4, 4, 3, 4, 3, 4, 3, 4, 4, 3]
            },
            "phq9_responses": [3, 3, 3, 2, 3, 2, 2, 2, 1],
            "gad7_responses": [3, 3, 3, 3, 2, 3, 2]
        }
    }
    
    test_mental_health_assessment(base_url, high_risk_payload, "High Risk")
    
    # Test 6: Assessment history
    print("\n6️⃣ Testing Assessment History...")
    try:
        response = requests.get(f"{base_url}/assessment-history/endpoint_test_low")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Assessment history retrieved")
            print(f"   📅 Timestamp: {result['timestamp']}")
            print(f"   📊 Has result data: {'result' in result}")
        elif response.status_code == 404:
            print(f"   ⚠️ No history found (expected for new user)")
        else:
            print(f"   ❌ History retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ History retrieval error: {e}")
    
    # Test 7: Error handling
    print("\n7️⃣ Testing Error Handling (Missing Country)...")
    error_payload = {
        "user_id": "endpoint_test_error",
        "assessment_data": {
            "age": 30,
            "gender": "Male",
            "stress_responses": {"work": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]},
            "phq9_responses": [1, 1, 1, 1, 1, 1, 1, 1, 0],
            "gad7_responses": [1, 1, 1, 1, 1, 1, 1]
            # Missing country field
        }
    }
    
    try:
        response = requests.post(f"{base_url}/mental-health-assessment", json=error_payload)
        if response.status_code == 400:
            print(f"   ✅ Error handling working correctly (400 Bad Request)")
        else:
            print(f"   ❌ Unexpected error response: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
    
    print("\n🎉 === ALL ENDPOINT TESTS COMPLETED ===")
    print("✅ Server is fully functional and ready for production use!")

def test_mental_health_assessment(base_url, payload, expected_risk):
    """Helper function to test mental health assessment"""
    try:
        response = requests.post(f"{base_url}/mental-health-assessment", json=payload)
        if response.status_code == 200:
            result = response.json()
            
            # Extract key information
            risk_level = result.get("assessments", {}).get("ml_risk_prediction", {}).get("risk_level", "Unknown")
            confidence = result.get("assessments", {}).get("ml_risk_prediction", {}).get("confidence", 0)
            
            # Check depression and anxiety scores
            dep_score = result.get("assessments", {}).get("depression_phq9", {}).get("total_score", 0)
            anx_score = result.get("assessments", {}).get("anxiety_gad7", {}).get("total_score", 0)
            
            print(f"   ✅ Assessment completed successfully")
            print(f"   🎯 Risk Level: {risk_level} (Confidence: {confidence:.1f}%)")
            print(f"   😔 Depression Score: {dep_score}/27")
            print(f"   😰 Anxiety Score: {anx_score}/21")
            print(f"   📋 Components: {len(result.keys())} main sections")
            
            # Check for recommendations and crisis resources
            rec_count = len(result.get("recommendations", []))
            crisis_count = len(result.get("crisis_resources", {}).get("resources", []))
            print(f"   💡 Recommendations: {rec_count}")
            print(f"   🆘 Crisis Resources: {crisis_count}")
            
        else:
            print(f"   ❌ Assessment failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Assessment error: {e}")

if __name__ == "__main__":
    test_all_endpoints()
