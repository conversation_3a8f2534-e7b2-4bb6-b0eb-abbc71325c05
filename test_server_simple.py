import requests
import json
import time

def test_server_health():
    """Test if the server is running and responding"""
    
    # Test basic health endpoint
    try:
        print("Testing server health...")
        response = requests.get("http://127.0.0.1:8002/", timeout=5)
        print(f"Health check status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Server is running!")
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server on port 8002")
        return False
    except Exception as e:
        print(f"❌ Error testing server: {e}")
        return False

def test_mental_health_endpoint():
    """Test the mental health assessment endpoint"""
    
    if not test_server_health():
        return
    
    url = "http://127.0.0.1:8002/mental-health-assessment"
    
    # Simple test payload
    payload = {
        "user_id": "test_user_simple",
        "assessment_data": {
            "age": 30,
            "gender": "Male",
            "country": "United States",
            "recent_stress_event": False,
            "stress_responses": {
                "work": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]
            },
            "phq9_responses": [1, 1, 1, 1, 1, 1, 1, 1, 0],
            "gad7_responses": [1, 1, 1, 1, 1, 1, 1]
        }
    }
    
    print("\n=== Testing Mental Health Assessment ===")
    print(f"URL: {url}")
    
    try:
        print("Sending request...")
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Response keys: {list(result.keys())}")
            
            # Check for key components
            if "assessments" in result:
                print("✅ Assessments found")
                assessments = result["assessments"]
                if "ml_risk_prediction" in assessments:
                    risk = assessments["ml_risk_prediction"]
                    print(f"  Risk Level: {risk.get('risk_level', 'Unknown')}")
                    print(f"  Confidence: {risk.get('confidence', 0):.1f}%")
            
            if "summary" in result:
                print("✅ Summary found")
                
            if "recommendations" in result:
                print(f"✅ Recommendations found: {len(result['recommendations'])} items")
                
            if "crisis_resources" in result:
                print("✅ Crisis resources found")
                
            print("\n🎉 Mental Health Assessment endpoint is working correctly!")
            
        elif response.status_code == 422:
            print("❌ 422 Validation Error")
            try:
                error_detail = response.json()
                print(f"Error: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Raw error: {response.text}")
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("=== Server Test ===")
    test_mental_health_endpoint()
