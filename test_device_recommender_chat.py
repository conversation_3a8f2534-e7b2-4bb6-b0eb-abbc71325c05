import requests
import json

def test_device_recommender_in_chat():
    """Test device recommender integration in the chat system"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🧪 === TESTING DEVICE RECOMMENDER IN CHAT ===\n")
    
    # Test user with sample health data
    user_id = "test_device_user"
    session_id = "device_test_session"
    
    # Step 1: Add some health data first
    print("1️⃣ Adding sample health data...")
    health_data = {
        "user_id": user_id,
        "health_data": {
            "Glucose": 120,
            "SpO2": 96,
            "ECG (Heart Rate)": 85,
            "Blood Pressure (Systolic)": 140,
            "Blood Pressure (Diastolic)": 90,
            "Weight (BMI)": 26.5,
            "Temperature": 37.0,
            "Malaria": "Negative",
            "Widal Test": "Negative",
            "Hepatitis B": "Negative",
            "Voluntary Serology": "Negative"
        }
    }
    
    try:
        response = requests.post(f"{base_url}/realtime-health-score", json=health_data)
        if response.status_code == 200:
            print("   ✅ Health data added successfully")
        else:
            print(f"   ❌ Failed to add health data: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error adding health data: {e}")
    
    # Step 2: Test direct device recommendation query
    print("\n2️⃣ Testing direct device recommendation query...")
    device_query = {
        "user_id": user_id,
        "session_id": session_id,
        "query": "Can you recommend some health devices for me?",
        "model": "qwen2.5:1.5b"
    }
    
    test_chat_query(base_url, device_query, "Direct device recommendation")
    
    # Step 3: Test device shopping query
    print("\n3️⃣ Testing device shopping query...")
    shopping_query = {
        "user_id": user_id,
        "session_id": session_id,
        "query": "What health devices should I buy to monitor my vitals at home?",
        "model": "qwen2.5:1.5b"
    }
    
    test_chat_query(base_url, shopping_query, "Device shopping")
    
    # Step 4: Test health monitoring query
    print("\n4️⃣ Testing health monitoring query...")
    monitoring_query = {
        "user_id": user_id,
        "session_id": session_id,
        "query": "I want to track my blood pressure and glucose at home",
        "model": "qwen2.5:1.5b"
    }
    
    test_chat_query(base_url, monitoring_query, "Health monitoring")
    
    # Step 5: Test general health query (should trigger proactive recommendation)
    print("\n5️⃣ Testing general health query...")
    general_query = {
        "user_id": user_id,
        "session_id": session_id,
        "query": "How can I improve my health?",
        "model": "qwen2.5:1.5b"
    }
    
    test_chat_query(base_url, general_query, "General health (proactive)")
    
    # Step 6: Test confirmation response
    print("\n6️⃣ Testing 'yes' confirmation...")
    yes_query = {
        "user_id": user_id,
        "session_id": session_id,
        "query": "yes",
        "model": "qwen2.5:1.5b"
    }
    
    test_chat_query(base_url, yes_query, "Yes confirmation")
    
    print("\n🎉 === DEVICE RECOMMENDER CHAT TESTING COMPLETED ===")

def test_chat_query(base_url, query_data, test_name):
    """Helper function to test a chat query"""
    try:
        response = requests.post(f"{base_url}/query", json=query_data)
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ {test_name} - SUCCESS")
            
            # Check response content
            response_text = result.get("response", "")
            tools_used = result.get("tools_used", [])
            
            print(f"   📝 Response length: {len(response_text)} characters")
            print(f"   🔧 Tools used: {', '.join(tools_used) if tools_used else 'None'}")
            
            # Check for device-related content
            device_indicators = [
                "turbomedics", "device", "monitor", "tracking", "health equipment",
                "blood pressure monitor", "glucose meter", "pulse oximeter"
            ]
            
            device_mentions = [indicator for indicator in device_indicators 
                             if indicator in response_text.lower()]
            
            if device_mentions:
                print(f"   🛒 Device content found: {', '.join(device_mentions)}")
            
            # Check for TurboMedics link
            if "turbomedics.com" in response_text.lower():
                print(f"   🔗 TurboMedics link included")
            
            # Show a snippet of the response
            snippet = response_text[:200] + "..." if len(response_text) > 200 else response_text
            print(f"   💬 Response snippet: {snippet}")
            
        else:
            print(f"   ❌ {test_name} - FAILED: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ {test_name} - ERROR: {e}")

if __name__ == "__main__":
    test_device_recommender_in_chat()
