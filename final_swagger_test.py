import requests
import json

def test_swagger_mental_health_final():
    """Final comprehensive test for Swagger UI"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🎯 === FINAL SWAGGER UI TEST ===\n")
    
    # Test payload for Swagger UI
    swagger_payload = {
        "user_id": "swagger_ui_test",
        "assessment_data": {
            "age": 30,
            "gender": "Female",
            "country": "United States",
            "recent_stress_event": True,
            "stress_responses": {
                "work": [3, 4, 3, 4, 3, 3, 4, 3, 4, 3],
                "relationship": [2, 3, 2, 3, 2, 2, 3, 2, 3, 2]
            },
            "phq9_responses": [2, 2, 1, 2, 1, 1, 2, 1, 0],
            "gad7_responses": [2, 2, 2, 1, 2, 1, 1]
        }
    }
    
    print("📋 Test Payload for Swagger UI:")
    print(json.dumps(swagger_payload, indent=2))
    print("\n" + "="*60 + "\n")
    
    # Execute the test
    try:
        print("🚀 Sending request to mental health assessment endpoint...")
        response = requests.post(f"{base_url}/mental-health-assessment", json=swagger_payload)
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ SUCCESS! Mental Health Assessment Response:")
            print(f"📋 Response Structure: {list(result.keys())}")
            
            # Detailed analysis of response
            if "assessments" in result:
                assessments = result["assessments"]
                print(f"\n🧠 ASSESSMENTS:")
                
                # ML Risk Prediction
                if "ml_risk_prediction" in assessments:
                    risk = assessments["ml_risk_prediction"]
                    print(f"  🎯 Risk Level: {risk.get('risk_level', 'Unknown')}")
                    print(f"  📊 Confidence: {risk.get('confidence', 0):.1f}%")
                    print(f"  🔍 Risk Factors: {len(risk.get('risk_factors', []))} identified")
                
                # Depression Assessment
                if "depression_phq9" in assessments:
                    dep = assessments["depression_phq9"]
                    print(f"  😔 Depression (PHQ-9): {dep.get('total_score', 0)}/27 - {dep.get('severity', 'Unknown')}")
                
                # Anxiety Assessment
                if "anxiety_gad7" in assessments:
                    anx = assessments["anxiety_gad7"]
                    print(f"  😰 Anxiety (GAD-7): {anx.get('total_score', 0)}/21 - {anx.get('severity', 'Unknown')}")
                
                # Stress/Burnout Assessment
                if "stress_burnout" in assessments:
                    stress = assessments["stress_burnout"]
                    print(f"  😓 Stress Categories:")
                    for category, scores in stress.items():
                        if isinstance(scores, dict):
                            total = scores.get('total_score', 0)
                            max_score = scores.get('max_score', 50)
                            interpretation = scores.get('interpretation', 'Unknown')
                            print(f"    • {category.title()}: {total}/{max_score} - {interpretation}")
            
            # Summary
            if "summary" in result:
                print(f"\n📝 SUMMARY:")
                print(f"  {result['summary']}")
            
            # Recommendations
            if "recommendations" in result and result["recommendations"]:
                print(f"\n💡 RECOMMENDATIONS ({len(result['recommendations'])}):")
                for i, rec in enumerate(result["recommendations"][:3], 1):
                    print(f"  {i}. {rec}")
                if len(result["recommendations"]) > 3:
                    print(f"  ... and {len(result['recommendations']) - 3} more")
            
            # Follow-up Reminders
            if "follow_up_reminders" in result and result["follow_up_reminders"]:
                print(f"\n📅 FOLLOW-UP REMINDERS ({len(result['follow_up_reminders'])}):")
                for i, reminder in enumerate(result["follow_up_reminders"][:2], 1):
                    print(f"  {i}. {reminder}")
            
            # Crisis Resources
            if "crisis_resources" in result and result["crisis_resources"]:
                crisis = result["crisis_resources"]
                if "resources" in crisis:
                    print(f"\n🆘 CRISIS RESOURCES ({len(crisis['resources'])}):")
                    for i, resource in enumerate(crisis["resources"][:2], 1):
                        print(f"  {i}. {resource}")
            
            # Metadata
            if "metadata" in result:
                meta = result["metadata"]
                print(f"\n📋 METADATA:")
                print(f"  User ID: {meta.get('user_id', 'Unknown')}")
                print(f"  Timestamp: {meta.get('timestamp', 'Unknown')}")
                print(f"  API Version: {meta.get('api_version', 'Unknown')}")
            
            print(f"\n🎉 PERFECT! The mental health assessment endpoint is working flawlessly!")
            print(f"📊 Total response size: {len(json.dumps(result))} characters")
            
        elif response.status_code == 422:
            print("❌ 422 Validation Error:")
            try:
                error_detail = response.json()
                print(json.dumps(error_detail, indent=2))
            except:
                print(f"Raw error response: {response.text}")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
    
    print(f"\n{'='*60}")
    print("🎯 SWAGGER UI TESTING INSTRUCTIONS:")
    print("1. Open http://127.0.0.1:8002/docs in your browser")
    print("2. Find the 'POST /mental-health-assessment' endpoint")
    print("3. Click 'Try it out'")
    print("4. Copy and paste the test payload shown above")
    print("5. Click 'Execute'")
    print("6. Verify you get a 200 response with comprehensive results")
    print(f"{'='*60}")

if __name__ == "__main__":
    test_swagger_mental_health_final()
