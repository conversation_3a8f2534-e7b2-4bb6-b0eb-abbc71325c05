import requests
import json

def test_health_data_storage():
    """Test that health data is properly stored and retrieved in chat history"""
    
    base_url = "http://127.0.0.1:8002"
    test_user_id = "test_health_storage"
    
    print("🧪 === TESTING HEALTH DATA STORAGE IN CHAT HISTORY ===\n")
    
    # Step 1: Add health data via realtime health score
    print("1️⃣ Adding health data via realtime health score...")
    health_data = {
        "user_id": test_user_id,
        "health_data": {
            "Glucose": 110,
            "SpO2": 98,
            "ECG (Heart Rate)": 75,
            "Blood Pressure (Systolic)": 120,
            "Blood Pressure (Diastolic)": 80,
            "Weight (BMI)": 24.5,
            "Temperature": 36.8,
            "Malaria": "Negative",
            "Widal Test": "Negative",
            "Hepatitis B": "Negative",
            "Voluntary Serology": "Negative"
        }
    }
    
    try:
        response = requests.post(f"{base_url}/realtime-health-score", json=health_data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Health data added successfully")
            print(f"   📊 Health Score: {result.get('Total Score', 'Unknown')}")
        else:
            print(f"   ❌ Failed to add health data: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error adding health data: {e}")
        return
    
    # Step 2: Check chat history to see if health data is stored
    print("\n2️⃣ Checking chat history for health data...")
    try:
        response = requests.get(f"{base_url}/chat-history", params={"user_id": test_user_id})
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ Chat history retrieved successfully")
            print(f"   💬 Messages: {len(result.get('messages', []))}")
            print(f"   📋 Health data categories: {list(result.get('health_data', {}).keys())}")
            
            # Check if realtime health score data is present
            health_data = result.get('health_data', {})
            if 'realtime_health_score' in health_data:
                print(f"   ✅ Realtime health score data found")
                rhs_data = health_data['realtime_health_score']
                print(f"   📅 Timestamp: {rhs_data.get('timestamp', 'Unknown')}")
                print(f"   📊 Data keys: {list(rhs_data.get('data', {}).keys())}")
            else:
                print(f"   ❌ Realtime health score data NOT found")
                
        else:
            print(f"   ❌ Failed to get chat history: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ Error getting chat history: {e}")
        return
    
    # Step 3: Send a chat message and check if device recommendations work
    print("\n3️⃣ Testing chat with device recommendations...")
    chat_query = {
        "user_id": test_user_id,
        "session_id": "test_session",
        "query": "Can you recommend some health devices?",
        "model": "qwen2.5:1.5b"
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=chat_query, timeout=30)
        if response.status_code == 200:
            result = response.json()
            
            print(f"   ✅ Chat query successful")
            response_text = result.get("response", "")
            print(f"   📝 Response length: {len(response_text)} characters")
            
            # Check for TurboMedics link
            if "turbomedics.com" in response_text.lower():
                print(f"   🔗 ✅ TurboMedics link included")
            else:
                print(f"   🔗 ❌ TurboMedics link missing")
            
            # Show snippet
            snippet = response_text[:100] + "..." if len(response_text) > 100 else response_text
            print(f"   💬 Response snippet: {snippet}")
            
        else:
            print(f"   ❌ Chat query failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Chat query error: {e}")
    
    # Step 4: Check chat history again to see updated messages
    print("\n4️⃣ Checking updated chat history...")
    try:
        response = requests.get(f"{base_url}/chat-history", params={"user_id": test_user_id})
        if response.status_code == 200:
            result = response.json()
            
            messages = result.get('messages', [])
            health_data = result.get('health_data', {})
            
            print(f"   ✅ Updated chat history retrieved")
            print(f"   💬 Total messages: {len(messages)}")
            print(f"   📋 Health data categories: {list(health_data.keys())}")
            
            # Show recent messages
            if messages:
                print(f"   📝 Recent messages:")
                for i, msg in enumerate(messages[-2:], 1):
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')[:50] + "..."
                    print(f"      {i}. {role}: {content}")
            
            # Verify health data is still there
            if 'realtime_health_score' in health_data:
                print(f"   ✅ Health data persistence confirmed")
            else:
                print(f"   ❌ Health data lost")
                
        else:
            print(f"   ❌ Failed to get updated chat history: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error getting updated chat history: {e}")
    
    print("\n🎉 === HEALTH DATA STORAGE TESTING COMPLETED ===")

if __name__ == "__main__":
    test_health_data_storage()
