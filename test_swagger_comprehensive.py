import requests
import json

def test_comprehensive_mental_health():
    """Comprehensive test of the mental health assessment API"""
    
    base_url = "http://127.0.0.1:8002"
    
    print("🧪 === COMPREHENSIVE MENTAL HEALTH API TEST ===\n")
    
    # Test 1: Health Check
    print("1️⃣ Testing Health Check...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Health check passed: {result['status']}")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Health check error: {e}")
    
    # Test 2: Supported Countries
    print("\n2️⃣ Testing Supported Countries...")
    try:
        response = requests.get(f"{base_url}/supported-countries")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Found {result['total_countries']} supported countries")
            print(f"   📍 Sample countries: {result['supported_countries'][:5]}")
        else:
            print(f"   ❌ Countries endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Countries endpoint error: {e}")
    
    # Test 3: Mental Health Assessment - Low Risk Case
    print("\n3️⃣ Testing Mental Health Assessment (Low Risk)...")
    low_risk_payload = {
        "user_id": "test_low_risk",
        "assessment_data": {
            "age": 25,
            "gender": "Female",
            "country": "United States",
            "recent_stress_event": False,
            "stress_responses": {
                "work": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2]
            },
            "phq9_responses": [0, 0, 1, 0, 0, 0, 0, 0, 0],
            "gad7_responses": [0, 1, 0, 0, 1, 0, 0]
        }
    }
    
    test_assessment(base_url, low_risk_payload, "Low Risk")
    
    # Test 4: Mental Health Assessment - Moderate Risk Case
    print("\n4️⃣ Testing Mental Health Assessment (Moderate Risk)...")
    moderate_risk_payload = {
        "user_id": "test_moderate_risk",
        "assessment_data": {
            "age": 35,
            "gender": "Male",
            "country": "Canada",
            "recent_stress_event": True,
            "stress_responses": {
                "work": [3, 4, 3, 4, 3, 3, 4, 3, 4, 3],
                "relationship": [3, 3, 2, 3, 2, 3, 2, 3, 3, 2]
            },
            "phq9_responses": [2, 2, 2, 1, 2, 1, 1, 1, 0],
            "gad7_responses": [2, 2, 2, 2, 1, 2, 1]
        }
    }
    
    test_assessment(base_url, moderate_risk_payload, "Moderate Risk")
    
    # Test 5: Mental Health Assessment - High Risk Case
    print("\n5️⃣ Testing Mental Health Assessment (High Risk)...")
    high_risk_payload = {
        "user_id": "test_high_risk",
        "assessment_data": {
            "age": 42,
            "gender": "Other",
            "country": "United Kingdom",
            "recent_stress_event": True,
            "stress_responses": {
                "work": [4, 5, 4, 5, 4, 4, 5, 4, 5, 4],
                "relationship": [4, 4, 3, 4, 3, 4, 3, 4, 4, 3],
                "medical": [4, 4, 4, 3, 4, 3, 4, 4, 3, 4]
            },
            "phq9_responses": [3, 3, 2, 2, 2, 2, 2, 1, 1],
            "gad7_responses": [3, 3, 3, 2, 3, 2, 2]
        }
    }
    
    test_assessment(base_url, high_risk_payload, "High Risk")
    
    # Test 6: Assessment History
    print("\n6️⃣ Testing Assessment History...")
    try:
        response = requests.get(f"{base_url}/assessment-history/test_low_risk")
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Retrieved assessment history")
            print(f"   📅 Timestamp: {result['timestamp']}")
            print(f"   📊 Result keys: {list(result['result'].keys())}")
        else:
            print(f"   ❌ History retrieval failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ History retrieval error: {e}")
    
    # Test 7: Error Handling - Missing Country
    print("\n7️⃣ Testing Error Handling (Missing Country)...")
    error_payload = {
        "user_id": "test_error",
        "assessment_data": {
            "age": 30,
            "gender": "Male",
            "stress_responses": {"work": [3, 3, 3, 3, 3, 3, 3, 3, 3, 3]},
            "phq9_responses": [1, 1, 1, 1, 1, 1, 1, 1, 0],
            "gad7_responses": [1, 1, 1, 1, 1, 1, 1]
        }
    }
    
    try:
        response = requests.post(f"{base_url}/mental-health-assessment", json=error_payload)
        if response.status_code == 400:
            print(f"   ✅ Correctly handled missing country (400 error)")
        else:
            print(f"   ❌ Unexpected response: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Error handling test failed: {e}")
    
    print("\n🎉 === COMPREHENSIVE TEST COMPLETED ===")

def test_assessment(base_url, payload, expected_risk):
    """Helper function to test an assessment"""
    try:
        response = requests.post(f"{base_url}/mental-health-assessment", json=payload)
        if response.status_code == 200:
            result = response.json()
            
            # Check risk level
            risk_level = result.get("assessments", {}).get("ml_risk_prediction", {}).get("risk_level", "Unknown")
            confidence = result.get("assessments", {}).get("ml_risk_prediction", {}).get("confidence", 0)
            
            print(f"   ✅ Assessment completed successfully")
            print(f"   🎯 Risk Level: {risk_level} (Expected: {expected_risk})")
            print(f"   📊 Confidence: {confidence:.1f}%")
            
            # Check components
            components = []
            if "assessments" in result:
                components.append("assessments")
            if "summary" in result:
                components.append("summary")
            if "recommendations" in result:
                components.append("recommendations")
            if "crisis_resources" in result:
                components.append("crisis_resources")
            if "follow_up_reminders" in result:
                components.append("follow_up_reminders")
            
            print(f"   📋 Components: {', '.join(components)}")
            
            # Check specific assessments
            assessments = result.get("assessments", {})
            if "depression_phq9" in assessments:
                dep = assessments["depression_phq9"]
                print(f"   😔 Depression: {dep.get('total_score', 0)}/27 - {dep.get('severity', 'Unknown')}")
            
            if "anxiety_gad7" in assessments:
                anx = assessments["anxiety_gad7"]
                print(f"   😰 Anxiety: {anx.get('total_score', 0)}/21 - {anx.get('severity', 'Unknown')}")
            
            if "stress_burnout" in assessments:
                stress = assessments["stress_burnout"]
                stress_categories = list(stress.keys())
                print(f"   😓 Stress categories: {', '.join(stress_categories)}")
            
        else:
            print(f"   ❌ Assessment failed: {response.status_code}")
            print(f"   📄 Response: {response.text}")
            
    except Exception as e:
        print(f"   ❌ Assessment error: {e}")

if __name__ == "__main__":
    test_comprehensive_mental_health()
