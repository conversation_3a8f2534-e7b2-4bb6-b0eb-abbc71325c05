#!/usr/bin/env python3
"""
Simple Mental Health Assessment Server
"""

import sys
import os
import json
import traceback
from datetime import datetime
from typing import Dict, Any

# Add tools path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

try:
    from fastapi import FastAPI, HTTPException
    from pydantic import BaseModel
    import uvicorn
    
    # Import the mental health assessment tool
    from tools.tools_mental_health_assessment import MentalHealthAssessmentTool
    
    print("✅ All imports successful")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

# Create FastAPI app
app = FastAPI(
    title="Simple Mental Health Assessment API",
    description="Focused API for mental health assessments",
    version="1.0.0"
)

# Request model
class MentalHealthAssessmentRequest(BaseModel):
    user_id: str
    assessment_data: Dict[str, Any]

# Storage for results (simple in-memory storage)
assessment_results = {}

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Simple Mental Health Assessment API is running",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    try:
        # Test tool initialization
        tool = MentalHealthAssessmentTool()
        return {
            "status": "healthy",
            "mental_health_tool": "initialized",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.post("/mental-health-assessment")
async def mental_health_assessment(request: MentalHealthAssessmentRequest):
    """
    Mental Health Assessment endpoint
    """
    try:
        user_id = request.user_id
        assessment_data = request.assessment_data
        
        print(f"📝 Processing assessment for user: {user_id}")
        print(f"📊 Assessment data keys: {list(assessment_data.keys())}")
        
        # Validate required fields
        if "country" not in assessment_data or not assessment_data["country"]:
            raise HTTPException(
                status_code=400, 
                detail="Country is required for mental health assessment"
            )
        
        # Initialize the mental health assessment tool
        print("🔧 Initializing mental health tool...")
        mental_health_tool = MentalHealthAssessmentTool()
        
        # Perform comprehensive assessment
        print("🧠 Performing comprehensive assessment...")
        result = mental_health_tool.comprehensive_assessment(assessment_data)
        
        # Store result
        assessment_results[user_id] = {
            "input": assessment_data,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
        print(f"✅ Assessment completed successfully for user: {user_id}")
        print(f"📋 Result keys: {list(result.keys())}")
        
        # Add some metadata
        result["metadata"] = {
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "api_version": "1.0.0"
        }
        
        return result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        error_msg = f"Error in mental health assessment: {str(e)}"
        print(f"❌ ERROR: {error_msg}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        
        raise HTTPException(
            status_code=500,
            detail=error_msg
        )

@app.get("/assessment-history/{user_id}")
async def get_assessment_history(user_id: str):
    """Get assessment history for a user"""
    if user_id in assessment_results:
        return assessment_results[user_id]
    else:
        raise HTTPException(
            status_code=404,
            detail=f"No assessment history found for user: {user_id}"
        )

@app.get("/supported-countries")
async def get_supported_countries():
    """Get list of supported countries for crisis resources"""
    try:
        tool = MentalHealthAssessmentTool()
        countries = tool.get_supported_countries()
        return {
            "supported_countries": countries,
            "total_countries": len(countries)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get supported countries: {str(e)}"
        )

if __name__ == "__main__":
    print("🚀 Starting Simple Mental Health Assessment Server...")
    print("📍 Server will be available at: http://127.0.0.1:8002")
    print("📖 API documentation at: http://127.0.0.1:8002/docs")
    
    try:
        uvicorn.run(
            app, 
            host="127.0.0.1", 
            port=8002,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
