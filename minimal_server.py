from fastapi import FastAPI
from pydantic import BaseModel
from typing import Dict, Any
import uvicorn
import sys
import os

# Add tools path
sys.path.append(os.path.join(os.path.dirname(__file__), 'tools'))

# Import the mental health assessment tool
from tools.tools_mental_health_assessment import MentalHealthAssessmentTool

app = FastAPI(title="Minimal Mental Health API")

class MentalHealthAssessmentRequest(BaseModel):
    user_id: str
    assessment_data: Dict[str, Any]

@app.get("/")
async def root():
    return {"message": "Minimal Mental Health API is running", "status": "OK"}

@app.post("/mental-health-assessment")
async def mental_health_assessment_endpoint(request: MentalHealthAssessmentRequest):
    """
    Minimal Mental Health Assessment endpoint for testing
    """
    try:
        user_id = request.user_id
        assessment_data = request.assessment_data
        
        print(f"Processing mental health assessment for user: {user_id}")
        print(f"Assessment data keys: {list(assessment_data.keys())}")
        
        # Validate that country is provided
        if "country" not in assessment_data or not assessment_data["country"]:
            return {"error": "Country is required for mental health assessment"}
        
        # Initialize the mental health assessment tool
        mental_health_tool = MentalHealthAssessmentTool()
        
        # Perform comprehensive assessment
        result = mental_health_tool.comprehensive_assessment(assessment_data)
        
        print(f"Mental health assessment completed successfully")
        print(f"Result keys: {list(result.keys())}")
        
        return result
        
    except Exception as e:
        import traceback
        error_msg = f"Error in mental health assessment: {str(e)}"
        print(f"ERROR: {error_msg}")
        print(f"Traceback: {traceback.format_exc()}")
        return {"error": error_msg}

if __name__ == "__main__":
    print("Starting Minimal Mental Health API server...")
    uvicorn.run(app, host="127.0.0.1", port=8003)
